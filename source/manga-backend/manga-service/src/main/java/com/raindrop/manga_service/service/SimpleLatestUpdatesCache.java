package com.raindrop.manga_service.service;

import com.raindrop.manga_service.dto.response.MangaSummaryResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * Simple cache service cho Latest Updates - chỉ cache page đầu tiên
 * Tránh phức tạp của JSON serialization
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SimpleLatestUpdatesCache {

    private final RedisTemplate<String, Object> redisTemplate;

    // TTL cho cache: 5 phút
    private static final long CACHE_TTL_SECONDS = 300;
    
    // Cache key cho việc đánh dấu cache đã được set
    private static final String CACHE_FLAG_KEY = "latest_updates:cached";

    /**
     * <PERSON><PERSON><PERSON> dấu rằng cache đã được invalidate
     */
    public void invalidateCache() {
        try {
            redisTemplate.delete(CACHE_FLAG_KEY);
            log.info("Latest updates cache invalidated");
        } catch (Exception e) {
            log.warn("Error invalidating cache: {}", e.getMessage());
        }
    }

    /**
     * Đánh dấu cache đã được set
     */
    public void markCacheAsSet() {
        try {
            redisTemplate.opsForValue().set(CACHE_FLAG_KEY, "1", CACHE_TTL_SECONDS, TimeUnit.SECONDS);
            log.debug("Latest updates cache marked as set");
        } catch (Exception e) {
            log.warn("Error marking cache: {}", e.getMessage());
        }
    }

    /**
     * Kiểm tra xem cache có hợp lệ không
     * @return true nếu cache hợp lệ, false nếu không
     */
    public boolean isCacheValid() {
        try {
            return redisTemplate.hasKey(CACHE_FLAG_KEY);
        } catch (Exception e) {
            log.warn("Error checking cache validity: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Kiểm tra xem có nên sử dụng cache không
     * Chỉ cache cho page đầu tiên với size 20
     */
    public boolean shouldUseCache(Pageable pageable) {
        return pageable.getPageNumber() == 0 && pageable.getPageSize() == 20;
    }
}
