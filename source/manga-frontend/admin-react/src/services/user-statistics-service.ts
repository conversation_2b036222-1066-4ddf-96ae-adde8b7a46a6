import { toast } from "react-toastify";
import { identityHttpClient } from "./http-client";
import { ApiResponse } from "../interfaces/models/ApiResponse";
import { logApiCall } from "../utils/api-logger";

// Interface cho thống kê người dùng
export interface UserStatisticsResponse {
    totalUsers: number;
    newUsersToday: number;
    newUsersThisWeek: number;
    newUsersThisMonth: number;
    usersByAuthProvider: Record<string, number>;
    usersByDay: Record<string, number>;
}

class UserStatisticsService {

}

export default new UserStatisticsService();
