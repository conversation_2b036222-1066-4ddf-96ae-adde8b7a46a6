import { toast } from "react-toastify";
import { mangaHttpClient } from "./http-client";
import { ApiResponse } from "../interfaces/models/ApiResponse";
import { logApiCall } from "../utils/api-logger";

// Interface cho truyện được xem nhiều nhất
export interface MostViewedMangaResponse {
    id: string;
    title: string;
    views: number;
    author: string;
    mainGenre: string;
}

class MangaStatisticsService {

}

export default new MangaStatisticsService();
